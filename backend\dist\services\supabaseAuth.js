"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupabaseAuthService = void 0;
const supabase_1 = require("@/config/supabase");
const logger_1 = require("@/utils/logger");
class SupabaseAuthService {
    static async register(data) {
        const { email, password, name } = data;
        try {
            logger_1.logger.info('Registering user with Supabase Auth', { email });
            const { data: authData, error: authError } = await supabase_1.supabase.auth.signUp({
                email,
                password,
                options: {
                    data: {
                        name: name
                    }
                }
            });
            if (authError) {
                logger_1.logger.error('Supabase auth registration error:', authError);
                throw new Error(authError.message);
            }
            if (!authData.user) {
                throw new Error('User registration failed - no user data returned');
            }
            try {
                await supabase_1.supabaseHelpers.createUser({
                    id: authData.user.id,
                    email: authData.user.email,
                    name: name,
                    subscription_plan: 'free',
                    api_tokens: {},
                    preferences: {}
                });
            }
            catch (profileError) {
                logger_1.logger.warn('Failed to create user profile, user may already exist:', profileError);
            }
            const { data: sessionData, error: sessionError } = await supabase_1.supabase.auth.getSession();
            if (sessionError || !sessionData.session) {
                logger_1.logger.error('Failed to get session after registration:', sessionError);
                throw new Error('Failed to create session');
            }
            const user = this.sanitizeUser({
                id: authData.user.id,
                email: authData.user.email,
                name: name,
                avatar: null,
                subscriptionPlan: 'free',
                apiTokens: {},
                preferences: {},
                createdAt: new Date(authData.user.created_at),
                updatedAt: new Date()
            });
            return {
                user,
                accessToken: sessionData.session.access_token,
                refreshToken: sessionData.session.refresh_token
            };
        }
        catch (error) {
            logger_1.logger.error('Registration error:', error);
            throw error;
        }
    }
    static async login(data) {
        const { email, password } = data;
        try {
            logger_1.logger.info('Logging in user with Supabase Auth', { email });
            const { data: authData, error: authError } = await supabase_1.supabase.auth.signInWithPassword({
                email,
                password
            });
            if (authError) {
                logger_1.logger.error('Supabase auth login error:', authError);
                throw new Error(authError.message);
            }
            if (!authData.user || !authData.session) {
                throw new Error('Login failed - no user or session data returned');
            }
            let userProfile;
            try {
                userProfile = await supabase_1.supabaseHelpers.getUserById(authData.user.id);
            }
            catch (error) {
                logger_1.logger.warn('User profile not found, creating one:', error);
                userProfile = await supabase_1.supabaseHelpers.createUser({
                    id: authData.user.id,
                    email: authData.user.email,
                    name: authData.user.user_metadata?.name || email.split('@')[0],
                    subscription_plan: 'free',
                    api_tokens: {},
                    preferences: {}
                });
            }
            const user = this.sanitizeUser({
                id: authData.user.id,
                email: authData.user.email,
                name: userProfile?.name || authData.user.user_metadata?.name || email.split('@')[0],
                avatar: userProfile?.avatar || null,
                subscriptionPlan: userProfile?.subscription_plan || 'free',
                apiTokens: userProfile?.api_tokens || {},
                preferences: userProfile?.preferences || {},
                createdAt: new Date(authData.user.created_at),
                updatedAt: new Date(userProfile?.updated_at || authData.user.updated_at)
            });
            return {
                user,
                accessToken: authData.session.access_token,
                refreshToken: authData.session.refresh_token
            };
        }
        catch (error) {
            logger_1.logger.error('Login error:', error);
            throw error;
        }
    }
    static async refreshToken(refreshToken) {
        try {
            const { data, error } = await supabase_1.supabase.auth.refreshSession({ refresh_token: refreshToken });
            if (error || !data.session) {
                throw new Error('Failed to refresh token');
            }
            return {
                accessToken: data.session.access_token,
                refreshToken: data.session.refresh_token
            };
        }
        catch (error) {
            logger_1.logger.error('Token refresh error:', error);
            throw error;
        }
    }
    static async logout(userId, refreshToken) {
        try {
            const { error } = await supabase_1.supabase.auth.signOut();
            if (error) {
                logger_1.logger.error('Supabase logout error:', error);
                throw error;
            }
            logger_1.logger.info('User logged out successfully', { userId });
        }
        catch (error) {
            logger_1.logger.error('Logout error:', error);
            throw error;
        }
    }
    static async verifyToken(token) {
        try {
            const { data: { user }, error } = await supabase_1.supabase.auth.getUser(token);
            if (error || !user) {
                return null;
            }
            let userProfile;
            try {
                userProfile = await supabase_1.supabaseHelpers.getUserById(user.id);
            }
            catch (error) {
                logger_1.logger.warn('User profile not found during token verification:', error);
                return null;
            }
            return this.sanitizeUser({
                id: user.id,
                email: user.email,
                name: userProfile?.name || user.user_metadata?.name || user.email.split('@')[0],
                avatar: userProfile?.avatar || null,
                subscriptionPlan: userProfile?.subscription_plan || 'free',
                apiTokens: userProfile?.api_tokens || {},
                preferences: userProfile?.preferences || {},
                createdAt: new Date(user.created_at),
                updatedAt: new Date(userProfile?.updated_at || user.updated_at)
            });
        }
        catch (error) {
            logger_1.logger.error('Token verification error:', error);
            return null;
        }
    }
    static async cleanupExpiredTokens() {
        logger_1.logger.info('Supabase handles token cleanup automatically');
    }
    static sanitizeUser(user) {
        return {
            id: user.id,
            email: user.email,
            name: user.name,
            avatar: user.avatar,
            subscriptionPlan: user.subscriptionPlan,
            apiTokens: user.apiTokens,
            preferences: user.preferences,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt
        };
    }
}
exports.SupabaseAuthService = SupabaseAuthService;
//# sourceMappingURL=supabaseAuth.js.map