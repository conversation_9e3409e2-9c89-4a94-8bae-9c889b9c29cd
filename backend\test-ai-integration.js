const http = require('http');

// Test configuration
const BASE_URL = 'http://localhost:3002';

// Helper function to make HTTP requests
function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Test AI functionality
async function testAIGeneration() {
  console.log('🤖 Testing AI Code Generation...');
  
  try {
    // First, register a test user to get auth token
    const testUser = {
      email: `ai_test_${Date.now()}@example.com`,
      password: 'testpassword123',
      name: 'AI Test User'
    };
    
    console.log('   📝 Registering test user...');
    const registerResponse = await makeRequest('/api/auth/register', 'POST', testUser);
    
    if (registerResponse.status !== 201) {
      console.log('   ❌ Failed to register user:', registerResponse.data);
      return;
    }
    
    const token = registerResponse.data.data.accessToken;
    console.log('   ✅ User registered and authenticated');
    
    // Test AI code generation
    console.log('   🚀 Testing AI code generation...');
    const aiRequest = {
      prompt: "Create a simple React Native button component that shows an alert when pressed",
      projectId: "test-project-123",
      sessionId: "test-session-456",
      language: "javascript",
      context: {
        framework: "react-native",
        platform: "mobile"
      }
    };
    
    const aiResponse = await makeRequest('/api/ai/generate', 'POST', aiRequest, {
      'Authorization': `Bearer ${token}`
    });
    
    if (aiResponse.status === 200) {
      console.log('   ✅ AI Code Generation Working!');
      console.log('   📄 Generated Code Preview:');
      const code = aiResponse.data.generatedCode?.code || 'No code generated';
      console.log('   ' + code.substring(0, 200) + (code.length > 200 ? '...' : ''));
      console.log('   🎯 Confidence:', aiResponse.data.generatedCode?.confidence || 'N/A');
      return true;
    } else {
      console.log('   ❌ AI Generation failed:', aiResponse.status, aiResponse.data);
      return false;
    }
    
  } catch (error) {
    console.log('   ❌ AI test error:', error.message);
    return false;
  }
}

async function testAIChat() {
  console.log('💬 Testing AI Chat...');
  
  try {
    // Register another test user
    const testUser = {
      email: `chat_test_${Date.now()}@example.com`,
      password: 'testpassword123',
      name: 'Chat Test User'
    };
    
    const registerResponse = await makeRequest('/api/auth/register', 'POST', testUser);
    if (registerResponse.status !== 201) {
      console.log('   ❌ Failed to register user for chat test');
      return false;
    }
    
    const token = registerResponse.data.data.accessToken;
    
    // Test AI chat
    const chatRequest = {
      message: "Hello! Can you help me create a mobile app?",
      projectId: "test-project-789",
      sessionId: "test-chat-session-123",
      includeContext: true
    };
    
    const chatResponse = await makeRequest('/api/ai/chat', 'POST', chatRequest, {
      'Authorization': `Bearer ${token}`
    });
    
    if (chatResponse.status === 200) {
      console.log('   ✅ AI Chat Working!');
      console.log('   💭 AI Response Preview:');
      const response = chatResponse.data.response || 'No response';
      console.log('   ' + response.substring(0, 200) + (response.length > 200 ? '...' : ''));
      return true;
    } else {
      console.log('   ❌ AI Chat failed:', chatResponse.status, chatResponse.data);
      return false;
    }
    
  } catch (error) {
    console.log('   ❌ Chat test error:', error.message);
    return false;
  }
}

// Run all AI tests
async function runAITests() {
  console.log('🚀 Starting AI Integration Tests...\n');
  
  const codeGenResult = await testAIGeneration();
  console.log('');
  
  const chatResult = await testAIChat();
  console.log('');
  
  console.log('🎉 AI Integration Tests Completed!');
  console.log('📊 Results:');
  console.log(`   🤖 Code Generation: ${codeGenResult ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`   💬 AI Chat: ${chatResult ? '✅ WORKING' : '❌ FAILED'}`);
  
  if (codeGenResult && chatResult) {
    console.log('\n🎊 CONGRATULATIONS! Your AI backend is fully functional with real OpenAI/Anthropic integration!');
  } else {
    console.log('\n⚠️  Some AI features may need attention. Check the logs above for details.');
  }
}

// Run the tests
runAITests().catch(console.error);
