{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T19:54:09.874Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T19:54:09.918Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:33:39.295Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:39.331Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:33:50.116Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:50.152Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:34:33.763Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:34:33.918Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:39:26.061Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:26.098Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:39:32.747Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:32.784Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:41:44.610Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:41:44.647Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:43:27.803Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:43:27.840Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:05.114Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:05.118Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:05.133Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.155Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.158Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.171Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:12.440Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:12.455Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:57:12.460Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.478Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.494Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.498Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:58:47.946Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:58:47.954Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:58:47.966Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.986Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.993Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:48.006Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:01.328Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:01.336Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.372Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:01.370Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.379Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.412Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:39.413Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:39.427Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T20:59:39.429Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.452Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.468Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.469Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T21:00:01.654Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T21:00:01.666Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.698Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T21:00:01.692Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.710Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.736Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:30:18.913Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:30:19.139Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:30:19.187Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:35:09.894Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:35:10.119Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:35:10.161Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.056Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.271Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"warn","message":"Database connection failed in development mode. Continuing without database... SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"warn","message":"Some features requiring database will not be available.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"info","message":"Server running on port 3001","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"Health check available at: http://localhost:3001/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:06.308Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:11.229Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:11.453Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:11.488Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.655Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.876Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:28.913Z"}
{"level":"warn","message":"Database connection failed in development mode. Continuing without database... SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:28.913Z"}
{"level":"warn","message":"Some features requiring database will not be available.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.914Z"}
{"level":"info","message":"Server running on port 3001","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"Health check available at: http://localhost:3001/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:28.915Z"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T22:36:48.729Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:34:14.835Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:34:15.068Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:34:15.105Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.083Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.316Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:07.352Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.353Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.353Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.354Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.354Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.355Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:07.355Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:24.417Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:35:24 +0000] \"GET /health HTTP/1.1\" 503 199 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4768\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:24.419Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:28.946Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:35:28 +0000] \"GET /health HTTP/1.1\" 503 199 \"-\" \"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4768\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:28.946Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.621Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.863Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:54.898Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.899Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.899Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.900Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.900Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.901Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:35:54.901Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.666Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.900Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:15.937Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.938Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.938Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.939Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.939Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.940Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:15.940Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:35.831Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.068Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:36.104Z"}
{"error":"SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","level":"warn","message":"Database connection failed. Continuing without database...","mode":"development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.104Z"}
{"level":"warn","message":"Features requiring database (auth, user management) will use in-memory fallbacks.","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.105Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:38:36.106Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:40:28.694Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:28 +0000] \"GET /health HTTP/1.1\" 503 200 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:28.696Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:28 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:28.698Z"}
{"level":"warn","message":"Database not available, using in-memory auth storage","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:28.873Z"}
{"level":"info","message":"User registered successfully (in-memory): <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.055Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:29 +0000] \"POST /api/auth/register HTTP/1.1\" 201 920 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.056Z"}
{"level":"warn","message":"Database not available, using in-memory auth storage","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.082Z"}
{"level":"info","message":"User logged in successfully (in-memory): <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.258Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:40:29 +0000] \"POST /api/auth/login HTTP/1.1\" 200 908 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:40:29.259Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.683Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.920Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.920Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.922Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.923Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.923Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:48.923Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:48:49.040Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:48:49.041Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:48:49.041Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:48:49.041Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:48:49.042Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:48:49.042Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.042Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.042Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:48:49.043Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.156Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.390Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.391Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.392Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.393Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.393Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.393Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:49:13.396Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:49:13.396Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:49:13.397Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:49:13.397Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:49:13.398Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:49:13.398Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.398Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.398Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.399Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.399Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.399Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:13.400Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:49:53 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:53.616Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:49:53 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:53.620Z"}
{"level":"error","message":"Registration error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.828Z"}
{"level":"error","message":"Registration endpoint error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.829Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:49:53 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:49:53.829Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:23.824Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.054Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.054Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.056Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.057Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.057Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.058Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:50:24.061Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:24.061Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:50:24.062Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:24.062Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:50:24.062Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:24.063Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.063Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.063Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:24.065Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.547Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.776Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.776Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.777Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.778Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.779Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.779Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:50:45.782Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:45.782Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:50:45.783Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:45.783Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:50:45.783Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:50:45.783Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.784Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.784Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:50:45.785Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:51:04 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:04.147Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:51:04 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:04.150Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration endpoint error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:51:04 +0000] \"POST /api/auth/register HTTP/1.1\" 500 69 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:04.398Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.547Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.792Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.793Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.794Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.795Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.795Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.795Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:51:39.798Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:39.799Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:51:39.799Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:39.800Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:51:39.800Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:39.800Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.800Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.800Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:39.802Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:54.964Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.199Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.199Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.201Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.202Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.203Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.203Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:51:55.206Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:55.206Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:51:55.206Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:55.207Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:51:55.207Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:51:55.207Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.207Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.207Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.208Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.208Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.208Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:51:55.209Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:13.854Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.109Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.110Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.112Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.114Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.114Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.114Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:52:14.117Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:14.118Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:52:14.119Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:14.119Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:52:14.119Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:14.119Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.120Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.120Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:14.121Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.258Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.543Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.544Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.545Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.547Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.547Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.547Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:52:35.551Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:35.551Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:52:35.551Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:35.552Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:52:35.552Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:52:35.552Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.553Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.553Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.554Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.555Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.555Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:35.555Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:53 +0000] \"GET /health HTTP/1.1\" 200 194 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.682Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:53 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.685Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.936Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:53 +0000] \"POST /api/auth/register HTTP/1.1\" 201 965 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:53.937Z"}
{"level":"info","message":"User logged in successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:54.125Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:52:54 +0000] \"POST /api/auth/login HTTP/1.1\" 200 953 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:52:54.126Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.263Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.550Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.551Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.553Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.554Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.555Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.555Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-30T23:54:22.558Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:54:22.559Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-30T23:54:22.559Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:54:22.560Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-30T23:54:22.560Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-30T23:54:22.560Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.561Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.561Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:54:22.563Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:55:27.820Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:55:27 +0000] \"POST /api/auth/register HTTP/1.1\" 201 979 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:55:27.823Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:58:12 +0000] \"POST /api/ai/generate HTTP/1.1\" - - \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:58:12.368Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:58:41.591Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:58:41 +0000] \"POST /api/auth/register HTTP/1.1\" 201 999 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:58:41.592Z"}
{"level":"info","message":"::1 - - [30/Jul/2025:23:59:48 +0000] \"POST /api/ai/chat HTTP/1.1\" - - \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-30T23:59:48.664Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.456Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.749Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.750Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.751Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.753Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.753Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.754Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:00:35.757Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:35.758Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:00:35.758Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:35.759Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:00:35.759Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:35.759Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.759Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.759Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:35.761Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.690Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.982Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.983Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.984Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.985Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.986Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.986Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:00:48.989Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:48.990Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:00:48.990Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:48.991Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:00:48.991Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:00:48.991Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.991Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.991Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:00:48.993Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.271Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.598Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.599Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.602Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.604Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.604Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.605Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:01:06.608Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:06.608Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:01:06.609Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:06.609Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:01:06.609Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:06.610Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.610Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.610Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:06.612Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.247Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.559Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.560Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.561Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.563Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.563Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.564Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:01:18.568Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:18.568Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:01:18.568Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:18.569Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:01:18.569Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:18.569Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.570Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.570Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:18.571Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:41.964Z"}
{"level":"info","message":"Initializing database connection...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.241Z"}
{"level":"info","message":"Initializing SQLite database...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.241Z"}
{"level":"info","message":"SQLite database initialized at: ./data/app.db","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.244Z"}
{"level":"info","message":"SQLite database connection established successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.245Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.246Z"}
{"level":"info","message":"Running database migrations...","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.246Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"-- Triggers to automatically update updated_at (SQLite version)\nCREATE TRIGGER IF NOT EXISTS update_","timestamp":"2025-07-31T00:01:42.250Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:42.251Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_projects_updated_at \n  AFTER UPDATE ON projects\n  FOR EACH ROW\n ","timestamp":"2025-07-31T00:01:42.251Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:42.251Z"}
{"error":"SQLITE_ERROR: incomplete input","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"CREATE TRIGGER IF NOT EXISTS update_ai_sessions_updated_at \n  AFTER UPDATE ON ai_sessions\n  FOR EACH","timestamp":"2025-07-31T00:01:42.252Z"}
{"error":"SQLITE_ERROR: cannot commit - no transaction is active","level":"warn","message":"Migration statement warning:","service":"mobile-app-builder-backend","statement":"END","timestamp":"2025-07-31T00:01:42.252Z"}
{"level":"info","message":"Database migrations completed successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.252Z"}
{"level":"info","message":"Database initialized successfully","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.252Z"}
{"level":"info","message":"Server running on port 3002","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"Environment: development","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"WebSocket server initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"Health check available at: http://localhost:3002/health","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:01:42.254Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:02:01.124Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:02:01 +0000] \"POST /api/auth/register HTTP/1.1\" 201 999 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:02:01.127Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:03:56 +0000] \"POST /api/ai/chat HTTP/1.1\" - - \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:03:56.959Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.177Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.179Z"}
{"level":"info","message":"OpenAI client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.182Z"}
{"level":"info","message":"Anthropic client initialized","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:04:30.182Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:30 +0000] \"GET /health HTTP/1.1\" 200 195 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:30.918Z"}
{"level":"info","message":"User registered successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.116Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:31 +0000] \"POST /api/auth/register HTTP/1.1\" 201 993 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.116Z"}
{"level":"info","message":"User logged in successfully: <EMAIL>","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.302Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:31 +0000] \"POST /api/auth/login HTTP/1.1\" 200 981 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.303Z"}
{"level":"info","message":"::1 - - [31/Jul/2025:00:05:31 +0000] \"GET / HTTP/1.1\" 200 259 \"-\" \"-\"","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:05:31.304Z"}
