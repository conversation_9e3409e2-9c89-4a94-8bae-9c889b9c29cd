const http = require('http');

// Test configuration
const BASE_URL = 'http://localhost:3002';

// Helper function to make HTTP requests
function makeRequest(path, method = 'GET', data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const options = {
      hostname: url.hostname,
      port: url.port,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function testSimpleAI() {
  console.log('🔍 Testing AI endpoint availability...');
  
  try {
    // First, register a test user to get auth token
    const testUser = {
      email: `simple_test_${Date.now()}@example.com`,
      password: 'testpassword123',
      name: 'Simple Test User'
    };
    
    console.log('   📝 Registering test user...');
    const registerResponse = await makeRequest('/api/auth/register', 'POST', testUser);
    
    if (registerResponse.status !== 201) {
      console.log('   ❌ Failed to register user:', registerResponse.data);
      return;
    }
    
    const token = registerResponse.data.data.accessToken;
    console.log('   ✅ User registered and authenticated');
    
    // Test simple AI chat endpoint
    console.log('   🤖 Testing simple AI chat...');
    const chatRequest = {
      message: "Hello",
      sessionId: "test-session-simple"
    };
    
    console.log('   📤 Sending request to /api/ai/chat...');
    const chatResponse = await makeRequest('/api/ai/chat', 'POST', chatRequest, {
      'Authorization': `Bearer ${token}`
    });
    
    console.log('   📥 Response status:', chatResponse.status);
    console.log('   📄 Response data:', JSON.stringify(chatResponse.data, null, 2));
    
    if (chatResponse.status === 200) {
      console.log('   ✅ AI Chat endpoint is working!');
    } else {
      console.log('   ❌ AI Chat endpoint failed');
    }
    
  } catch (error) {
    console.log('   ❌ Test error:', error.message);
  }
}

// Run the test
testSimpleAI().catch(console.error);
