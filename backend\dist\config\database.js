"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = exports.pool = exports.db = void 0;
const pg_1 = require("pg");
const sqlite3_1 = __importDefault(require("sqlite3"));
const sqlite_1 = require("sqlite");
const logger_1 = require("@/utils/logger");
const DB_TYPE = process.env.DB_TYPE || 'sqlite';
console.log('DB_TYPE from environment:', process.env.DB_TYPE);
console.log('DB_TYPE resolved to:', DB_TYPE);
const supabase_1 = require("./supabase");
let sqliteDb = null;
let pgPool = null;
const getDatabaseConfig = () => {
    if (process.env.DATABASE_URL) {
        return {
            connectionString: process.env.DATABASE_URL,
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        };
    }
    const config = {
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432'),
        database: process.env.DB_NAME || 'mobile_app_builder',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        max: 20,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000,
    };
    if (config.password === undefined || config.password === null) {
        config.password = '';
    }
    return config;
};
if (DB_TYPE === 'postgresql') {
    pgPool = new pg_1.Pool(getDatabaseConfig());
}
if (pgPool) {
    pgPool.on('connect', () => {
        logger_1.logger.info('Connected to PostgreSQL database');
    });
    pgPool.on('error', (err) => {
        logger_1.logger.error('PostgreSQL connection error:', err);
        process.exit(-1);
    });
}
const initializeSQLite = async () => {
    const dbPath = process.env.SQLITE_DB_PATH || './data/app.db';
    const fs = require('fs');
    const path = require('path');
    const dir = path.dirname(dbPath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    const db = await (0, sqlite_1.open)({
        filename: dbPath,
        driver: sqlite3_1.default.Database
    });
    logger_1.logger.info(`SQLite database initialized at: ${dbPath}`);
    return db;
};
class SQLiteAdapter {
    constructor(db) {
        this.db = db;
    }
    async query(text, params = []) {
        const sqliteQuery = text
            .replace(/\$(\d+)/g, '?')
            .replace(/RETURNING \*/g, '')
            .replace(/SERIAL/g, 'INTEGER PRIMARY KEY AUTOINCREMENT')
            .replace(/TIMESTAMP WITH TIME ZONE/g, 'DATETIME')
            .replace(/JSONB/g, 'TEXT')
            .replace(/TEXT\[\]/g, 'TEXT');
        if (text.includes('INSERT') && text.includes('RETURNING')) {
            const tableName = text.match(/INSERT INTO (\w+)/)?.[1];
            await this.db.run(sqliteQuery, params);
            if (tableName && params.length > 0) {
                const row = await this.db.get(`SELECT * FROM ${tableName} WHERE id = ?`, [params[0]]);
                return { rows: row ? [row] : [] };
            }
            return { rows: [] };
        }
        else if (text.includes('SELECT')) {
            const rows = await this.db.all(sqliteQuery, params);
            return { rows };
        }
        else {
            const result = await this.db.run(sqliteQuery, params);
            return { rows: [], changes: result.changes };
        }
    }
    async close() {
        await this.db.close();
    }
}
class PostgreSQLAdapter {
    constructor(pool) {
        this.pool = pool;
    }
    async query(text, params = []) {
        const client = await this.pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        }
        finally {
            client.release();
        }
    }
    async close() {
        await this.pool.end();
    }
}
exports.pool = DB_TYPE === 'supabase' ? supabase_1.pool : {
    query: async (text, params) => {
        return await exports.db.query(text, params);
    },
    connect: async () => {
        return {
            query: async (text, params) => {
                return await exports.db.query(text, params);
            },
            release: () => { }
        };
    }
};
const initializeDatabase = async () => {
    try {
        if (DB_TYPE === 'supabase') {
            logger_1.logger.info('Initializing Supabase database...');
            await (0, supabase_1.initializeDatabase)();
            exports.db = supabase_1.db;
            logger_1.logger.info('Supabase database connection established successfully');
        }
        else if (DB_TYPE === 'sqlite') {
            logger_1.logger.info('Initializing SQLite database...');
            sqliteDb = await initializeSQLite();
            exports.db = new SQLiteAdapter(sqliteDb);
            await exports.db.query('SELECT 1');
            logger_1.logger.info('SQLite database connection established successfully');
        }
        else {
            logger_1.logger.info('Initializing PostgreSQL database...');
            if (!pgPool) {
                throw new Error('PostgreSQL pool not initialized');
            }
            exports.db = new PostgreSQLAdapter(pgPool);
            await exports.db.query('SELECT NOW()');
            logger_1.logger.info('PostgreSQL database connection established successfully');
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to database:', error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
//# sourceMappingURL=database.js.map