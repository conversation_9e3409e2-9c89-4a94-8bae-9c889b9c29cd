"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiService = exports.AIService = void 0;
const openai_1 = __importDefault(require("openai"));
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const pinecone_1 = require("@pinecone-database/pinecone");
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
class AIService {
    constructor() {
        this.sessions = new Map();
        this.contextVectors = new Map();
        this.initializeProviders();
    }
    initializeProviders() {
        try {
            if (process.env.OPENAI_API_KEY) {
                this.openai = new openai_1.default({
                    apiKey: process.env.OPENAI_API_KEY,
                });
                logger_1.logger.info('OpenAI client initialized');
            }
            if (process.env.ANTHROPIC_API_KEY) {
                this.anthropic = new sdk_1.default({
                    apiKey: process.env.ANTHROPIC_API_KEY,
                });
                logger_1.logger.info('Anthropic client initialized');
            }
            if (process.env.PINECONE_API_KEY) {
                this.pinecone = new pinecone_1.Pinecone({
                    apiKey: process.env.PINECONE_API_KEY,
                });
                logger_1.logger.info('Pinecone client initialized');
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize AI providers:', error);
        }
    }
    async generateCode(request) {
        try {
            const sessionId = request.sessionId || (0, uuid_1.v4)();
            const session = await this.getOrCreateSession(sessionId, request.projectId);
            const context = await this.buildProjectContext(request.projectId);
            const systemPrompt = this.buildSystemPrompt(context, request.language);
            const userPrompt = this.buildUserPrompt(request, context);
            const generatedCode = await this.callAIProvider(systemPrompt, userPrompt, session);
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'prompt',
                content: request.prompt,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'response',
                content: generatedCode.code,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            await this.updateContextVectors(sessionId, request.prompt, generatedCode.code);
            return generatedCode;
        }
        catch (error) {
            logger_1.logger.error('Error generating code:', error);
            throw new Error(`Failed to generate code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async explainCode(request) {
        try {
            const sessionId = request.sessionId || (0, uuid_1.v4)();
            const session = await this.getOrCreateSession(sessionId, request.projectId);
            const systemPrompt = `You are an expert React Native developer. Explain the provided code clearly and concisely. 
      Focus on what the code does, how it works, and any React Native specific concepts.
      Provide explanations that are helpful for developers of all skill levels.`;
            const userPrompt = `Please explain this ${request.language} code:\n\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\nFile: ${request.filePath || 'Unknown'}`;
            const explanation = await this.callAIProviderForText(systemPrompt, userPrompt, session);
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'prompt',
                content: `Explain code: ${request.code.substring(0, 100)}...`,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'response',
                content: explanation,
                timestamp: new Date(),
            });
            return explanation;
        }
        catch (error) {
            logger_1.logger.error('Error explaining code:', error);
            throw new Error(`Failed to explain code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async fixCode(request) {
        try {
            const sessionId = request.sessionId || (0, uuid_1.v4)();
            const session = await this.getOrCreateSession(sessionId, request.projectId);
            const systemPrompt = `You are an expert React Native developer. Fix the provided code based on the error message.
      Return only the corrected code with a brief explanation of what was fixed.
      Ensure the fix follows React Native best practices and maintains the original functionality.`;
            const userPrompt = `Fix this ${request.language} code that has an error:\n\nError: ${request.error}\n\nCode:\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\nFile: ${request.filePath}`;
            const response = await this.callAIProviderForText(systemPrompt, userPrompt, session);
            const codeFix = this.parseCodeFixResponse(response, request);
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'prompt',
                content: `Fix error: ${request.error}`,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'response',
                content: codeFix.fixedCode,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            return codeFix;
        }
        catch (error) {
            logger_1.logger.error('Error fixing code:', error);
            throw new Error(`Failed to fix code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async modifyCode(request) {
        try {
            const sessionId = request.sessionId || (0, uuid_1.v4)();
            const session = await this.getOrCreateSession(sessionId, request.projectId);
            const systemPrompt = `You are an expert React Native developer. Modify the provided code according to the instruction.
      Return the modified code and explain what changes were made.
      Ensure the modifications follow React Native best practices and maintain code quality.`;
            const userPrompt = `Modify this ${request.language} code:\n\nInstruction: ${request.instruction}\n\nCurrent code:\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\nFile: ${request.filePath}`;
            const response = await this.callAIProviderForText(systemPrompt, userPrompt, session);
            const codeDiff = this.createCodeDiff(request.code, response, request.filePath);
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'prompt',
                content: `Modify code: ${request.instruction}`,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'response',
                content: codeDiff.newCode,
                timestamp: new Date(),
                metadata: {
                    filePath: request.filePath,
                    language: request.language,
                },
            });
            return codeDiff;
        }
        catch (error) {
            logger_1.logger.error('Error modifying code:', error);
            throw new Error(`Failed to modify code: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async chat(request) {
        try {
            const sessionId = request.sessionId || (0, uuid_1.v4)();
            const session = await this.getOrCreateSession(sessionId, request.projectId);
            let systemPrompt = `You are an AI assistant specialized in React Native development. 
      You help developers build mobile applications using React Native.
      Provide helpful, accurate, and practical advice.`;
            let userPrompt = request.message;
            if (request.includeContext) {
                const context = await this.buildProjectContext(request.projectId);
                systemPrompt += `\n\nProject Context:\n${this.formatProjectContext(context)}`;
            }
            const conversationHistory = this.formatConversationHistory(session);
            if (conversationHistory) {
                userPrompt = `${conversationHistory}\n\nUser: ${request.message}`;
            }
            const response = await this.callAIProviderForText(systemPrompt, userPrompt, session);
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'prompt',
                content: request.message,
                timestamp: new Date(),
            });
            await this.storeInteraction(session, {
                id: (0, uuid_1.v4)(),
                sessionId,
                type: 'response',
                content: response,
                timestamp: new Date(),
            });
            return response;
        }
        catch (error) {
            logger_1.logger.error('Error in chat:', error);
            throw new Error(`Failed to process chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async suggestImprovements(code, language, filePath) {
        try {
            const systemPrompt = `You are an expert React Native code reviewer. 
      Analyze the provided code and suggest improvements for performance, security, best practices, and potential bugs.
      Return suggestions in a structured format.`;
            const userPrompt = `Review this ${language} code and suggest improvements:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nFile: ${filePath}`;
            const response = await this.callAIProviderForText(systemPrompt, userPrompt);
            return this.parseSuggestions(response, filePath);
        }
        catch (error) {
            logger_1.logger.error('Error suggesting improvements:', error);
            return [];
        }
    }
    async getOrCreateSession(sessionId, projectId) {
        if (this.sessions.has(sessionId)) {
            return this.sessions.get(sessionId);
        }
        const session = {
            id: sessionId,
            projectId,
            userId: '',
            conversationHistory: [],
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        this.sessions.set(sessionId, session);
        return session;
    }
    async buildProjectContext(projectId) {
        return {
            projectId,
            files: [],
            dependencies: ['react', 'react-native'],
            reactNativeVersion: '0.72.0',
            targetPlatforms: ['ios', 'android'],
            recentChanges: [],
        };
    }
    buildSystemPrompt(context, language) {
        return `You are an expert React Native developer. Generate high-quality, production-ready code.

Project Context:
- React Native Version: ${context.reactNativeVersion}
- Target Platforms: ${context.targetPlatforms.join(', ')}
- Dependencies: ${context.dependencies.join(', ')}
- Language: ${language || 'TypeScript'}

Guidelines:
1. Use TypeScript when possible for better type safety
2. Follow React Native best practices and conventions
3. Use functional components with hooks
4. Implement proper error handling
5. Add appropriate comments for complex logic
6. Ensure cross-platform compatibility
7. Use proper styling with StyleSheet
8. Handle platform-specific code when necessary

Return only the code with brief explanations when needed.`;
    }
    buildUserPrompt(request, context) {
        let prompt = `Generate React Native code for: ${request.prompt}`;
        if (request.filePath) {
            prompt += `\nFile: ${request.filePath}`;
        }
        if (request.context?.existingCode) {
            prompt += `\n\nExisting code to modify or extend:\n\`\`\`\n${request.context.existingCode}\n\`\`\``;
        }
        if (request.context?.targetPlatform) {
            prompt += `\nTarget platform: ${request.context.targetPlatform}`;
        }
        return prompt;
    }
    async callAIProvider(systemPrompt, userPrompt, session) {
        if (this.openai) {
            return await this.callOpenAI(systemPrompt, userPrompt);
        }
        else if (this.anthropic) {
            return await this.callAnthropic(systemPrompt, userPrompt);
        }
        else {
            throw new Error('No AI provider available');
        }
    }
    async callAIProviderForText(systemPrompt, userPrompt, session) {
        if (this.openai) {
            return await this.callOpenAIForText(systemPrompt, userPrompt);
        }
        else if (this.anthropic) {
            return await this.callAnthropicForText(systemPrompt, userPrompt);
        }
        else {
            throw new Error('No AI provider available');
        }
    }
    async callOpenAI(systemPrompt, userPrompt) {
        const model = process.env.OPENAI_MODEL || 'gpt-4o-mini';
        const response = await this.openai.chat.completions.create({
            model,
            messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ],
            temperature: 0.7,
            max_tokens: 2000,
        });
        const content = response.choices[0]?.message?.content || '';
        return this.parseGeneratedCode(content);
    }
    async callOpenAIForText(systemPrompt, userPrompt) {
        const model = process.env.OPENAI_MODEL || 'gpt-4o-mini';
        const response = await this.openai.chat.completions.create({
            model,
            messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ],
            temperature: 0.7,
            max_tokens: 1500,
        });
        return response.choices[0]?.message?.content || '';
    }
    async callAnthropic(systemPrompt, userPrompt) {
        const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022';
        const response = await this.anthropic.messages.create({
            model,
            max_tokens: 2000,
            system: systemPrompt,
            messages: [
                { role: 'user', content: userPrompt }
            ],
        });
        const content = response.content[0]?.type === 'text' ? response.content[0].text : '';
        return this.parseGeneratedCode(content);
    }
    async callAnthropicForText(systemPrompt, userPrompt) {
        const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022';
        const response = await this.anthropic.messages.create({
            model,
            max_tokens: 1500,
            system: systemPrompt,
            messages: [
                { role: 'user', content: userPrompt }
            ],
        });
        return response.content[0]?.type === 'text' ? response.content[0].text : '';
    }
    parseGeneratedCode(content) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const matches = Array.from(content.matchAll(codeBlockRegex));
        if (matches.length > 0) {
            const [, language, code] = matches[0];
            return {
                code: code.trim(),
                language: language || 'typescript',
                explanation: content.replace(codeBlockRegex, '').trim(),
                confidence: 0.8,
            };
        }
        return {
            code: content.trim(),
            language: 'typescript',
            explanation: 'Generated code',
            confidence: 0.6,
        };
    }
    parseCodeFixResponse(response, request) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const matches = Array.from(response.matchAll(codeBlockRegex));
        let fixedCode = request.code;
        let explanation = response;
        if (matches.length > 0) {
            fixedCode = matches[0][2].trim();
            explanation = response.replace(codeBlockRegex, '').trim();
        }
        return {
            originalCode: request.code,
            fixedCode,
            explanation,
            confidence: 0.8,
            filePath: request.filePath,
        };
    }
    createCodeDiff(oldCode, newCodeResponse, filePath) {
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        const matches = Array.from(newCodeResponse.matchAll(codeBlockRegex));
        let newCode = newCodeResponse;
        let summary = 'Code modified';
        if (matches.length > 0) {
            newCode = matches[0][2].trim();
            summary = newCodeResponse.replace(codeBlockRegex, '').trim();
        }
        const changes = [];
        const oldLines = oldCode.split('\n');
        const newLines = newCode.split('\n');
        const maxLines = Math.max(oldLines.length, newLines.length);
        for (let i = 0; i < maxLines; i++) {
            const oldLine = oldLines[i] || '';
            const newLine = newLines[i] || '';
            if (oldLine !== newLine) {
                if (!oldLine) {
                    changes.push({
                        type: 'add',
                        lineNumber: i + 1,
                        content: newLine,
                    });
                }
                else if (!newLine) {
                    changes.push({
                        type: 'remove',
                        lineNumber: i + 1,
                        content: oldLine,
                    });
                }
                else {
                    changes.push({
                        type: 'modify',
                        lineNumber: i + 1,
                        content: newLine,
                        context: oldLine,
                    });
                }
            }
        }
        return {
            filePath,
            oldCode,
            newCode,
            changes,
            summary,
        };
    }
    parseSuggestions(response, filePath) {
        const suggestions = [];
        const lines = response.split('\n');
        for (const line of lines) {
            if (line.includes('performance') || line.includes('Performance')) {
                suggestions.push({
                    type: 'performance',
                    title: 'Performance Improvement',
                    description: line.trim(),
                    filePath,
                    severity: 'medium',
                });
            }
            else if (line.includes('security') || line.includes('Security')) {
                suggestions.push({
                    type: 'security',
                    title: 'Security Issue',
                    description: line.trim(),
                    filePath,
                    severity: 'high',
                });
            }
            else if (line.includes('bug') || line.includes('Bug') || line.includes('error')) {
                suggestions.push({
                    type: 'bug-fix',
                    title: 'Potential Bug',
                    description: line.trim(),
                    filePath,
                    severity: 'high',
                });
            }
            else if (line.includes('best practice') || line.includes('Best practice')) {
                suggestions.push({
                    type: 'best-practice',
                    title: 'Best Practice',
                    description: line.trim(),
                    filePath,
                    severity: 'low',
                });
            }
        }
        return suggestions;
    }
    formatProjectContext(context) {
        return `Project: ${context.projectId}
React Native: ${context.reactNativeVersion}
Platforms: ${context.targetPlatforms.join(', ')}
Dependencies: ${context.dependencies.join(', ')}
Files: ${context.files.length} files`;
    }
    formatConversationHistory(session) {
        const recentHistory = session.conversationHistory.slice(-6);
        return recentHistory
            .map(interaction => `${interaction.type === 'prompt' ? 'User' : 'Assistant'}: ${interaction.content}`)
            .join('\n');
    }
    async storeInteraction(session, interaction) {
        session.conversationHistory.push(interaction);
        session.updatedAt = new Date();
        if (session.conversationHistory.length > 50) {
            session.conversationHistory = session.conversationHistory.slice(-50);
        }
    }
    async updateContextVectors(sessionId, prompt, response) {
        if (!this.contextVectors.has(sessionId)) {
            this.contextVectors.set(sessionId, []);
        }
        const vectors = this.contextVectors.get(sessionId);
        vectors.push({
            id: (0, uuid_1.v4)(),
            projectId: '',
            sessionId,
            content: prompt,
            embedding: [],
            metadata: {
                type: 'prompt',
                timestamp: new Date(),
            },
        });
        vectors.push({
            id: (0, uuid_1.v4)(),
            projectId: '',
            sessionId,
            content: response,
            embedding: [],
            metadata: {
                type: 'response',
                timestamp: new Date(),
            },
        });
    }
    async maintainContext(sessionId, interaction) {
        const session = this.sessions.get(sessionId);
        if (session) {
            await this.storeInteraction(session, interaction);
        }
    }
    async getSessionHistory(sessionId) {
        const session = this.sessions.get(sessionId);
        return session?.conversationHistory || [];
    }
    async clearSession(sessionId) {
        this.sessions.delete(sessionId);
        this.contextVectors.delete(sessionId);
    }
    async healthCheck() {
        return {
            openai: !!this.openai,
            anthropic: !!this.anthropic,
            pinecone: !!this.pinecone,
        };
    }
}
exports.AIService = AIService;
exports.aiService = new AIService();
//# sourceMappingURL=aiService.js.map