{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T19:54:09.918Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:39.331Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:33:50.152Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:34:33.918Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:26.098Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:39:32.784Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:41:44.647Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:43:27.840Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.155Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.158Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:05.171Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.478Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.494Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:57:12.498Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.986Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:47.993Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:58:48.006Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.372Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.379Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:01.412Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.452Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.468Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:141:5)","timestamp":"2025-07-30T20:59:39.469Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.698Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.710Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:142:5)","timestamp":"2025-07-30T21:00:01.736Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:30:19.187Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:144:5)","timestamp":"2025-07-30T22:35:10.161Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:06.306Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:11.488Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:30:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:145:7)","timestamp":"2025-07-30T22:36:28.913Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:34:15.105Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:07.352Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:24.417Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:35:28.946Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:35:54.898Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:15.937Z"}
{"level":"error","message":"Failed to connect to database: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async initializeDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\database.ts:52:20)\n    at async startServer (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\server.ts:147:7)","timestamp":"2025-07-30T23:38:36.104Z"}
{"level":"error","message":"Database health check failed: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string","service":"mobile-app-builder-backend","stack":"Error: SASL: SCRAM-SERVER-FIRST-MESSAGE: client password must be a string\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\pg-pool\\index.js:45:11\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:7","timestamp":"2025-07-30T23:40:28.694Z"}
{"level":"error","message":"Registration error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.828Z"}
{"level":"error","message":"Registration endpoint error: Cannot read properties of undefined (reading 'id')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'id')\n    at Function.registerWithDatabase (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:76:21)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:31:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-30T23:49:53.829Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"code":"SQLITE_RANGE","errno":25,"level":"error","message":"Registration endpoint error: SQLITE_RANGE: column index out of range","service":"mobile-app-builder-backend","stack":"Error: SQLITE_RANGE: column index out of range","timestamp":"2025-07-30T23:51:04.397Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Registration error: SQLITE_ERROR: no such table: users","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: no such table: users","timestamp":"2025-07-31T00:14:23.133Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Registration endpoint error: SQLITE_ERROR: no such table: users","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: no such table: users","timestamp":"2025-07-31T00:14:23.133Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:19:11.439Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:25:27.031Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:25:27.040Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:25:43.961Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:25:45.713Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:26:16.654Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:26:52.051Z"}
{"code":"SQLITE_ERROR","errno":1,"level":"error","message":"Failed to run database migrations: SQLITE_ERROR: near \"EXTENSION\": syntax error","service":"mobile-app-builder-backend","stack":"Error: SQLITE_ERROR: near \"EXTENSION\": syntax error","timestamp":"2025-07-31T00:27:10.367Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.998Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:39.998Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.037Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:27:55.037Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.826Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:16.827Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.708Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:28:42.708Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:28:54.898Z"}
{"__isAuthError":true,"code":"email_address_invalid","level":"error","message":"Supabase auth registration error: Email address \"<EMAIL>\" is invalid","name":"AuthApiError","service":"mobile-app-builder-backend","stack":"AuthApiError: Email address \"<EMAIL>\" is invalid\n    at handleError (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:102:9)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async _handleRequest (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:195:5)\n    at async _request (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:157:16)\n    at async SupabaseAuthClient.signUp (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts:502:15)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:13:52)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","status":400,"timestamp":"2025-07-31T00:28:55.392Z"}
{"level":"error","message":"Registration error: Email address \"<EMAIL>\" is invalid","service":"mobile-app-builder-backend","stack":"Error: Email address \"<EMAIL>\" is invalid\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:25:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:28:55.392Z"}
{"level":"error","message":"Registration error: Email address \"<EMAIL>\" is invalid","service":"mobile-app-builder-backend","stack":"Error: Email address \"<EMAIL>\" is invalid\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:25:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:28:55.393Z"}
{"level":"error","message":"Registration endpoint error: Email address \"<EMAIL>\" is invalid","service":"mobile-app-builder-backend","stack":"Error: Email address \"<EMAIL>\" is invalid\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:25:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:28:55.393Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:29:30.793Z"}
{"level":"error","message":"Failed to get session after registration:","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:29:31.720Z"}
{"level":"error","message":"Registration error: Failed to create session","service":"mobile-app-builder-backend","stack":"Error: Failed to create session\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:51:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:29:31.720Z"}
{"level":"error","message":"Registration error: Failed to create session","service":"mobile-app-builder-backend","stack":"Error: Failed to create session\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:51:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:29:31.721Z"}
{"level":"error","message":"Registration endpoint error: Failed to create session","service":"mobile-app-builder-backend","stack":"Error: Failed to create session\n    at Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:51:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.register (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:39:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:41:20","timestamp":"2025-07-31T00:29:31.721Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to Supabase: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.644Z"}
{"code":"42P01","details":null,"hint":null,"level":"error","message":"Failed to connect to database: relation \"public.user_profiles\" does not exist","service":"mobile-app-builder-backend","timestamp":"2025-07-31T00:30:01.645Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:30:08.035Z"}
{"__isAuthError":true,"code":"email_not_confirmed","level":"error","message":"Supabase auth login error: Email not confirmed","name":"AuthApiError","service":"mobile-app-builder-backend","stack":"AuthApiError: Email not confirmed\n    at handleError (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:102:9)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async _handleRequest (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:195:5)\n    at async _request (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:157:16)\n    at async SupabaseAuthClient.signInWithPassword (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts:573:15)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:89:52)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","status":400,"timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Login endpoint error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:08.822Z"}
{"level":"error","message":"Database health check failed: Cannot read properties of undefined (reading 'query')","service":"mobile-app-builder-backend","stack":"TypeError: Cannot read properties of undefined (reading 'query')\n    at Object.query (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\config\\supabase.ts:41:21)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\health.ts:29:18\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-07-31T00:30:39.731Z"}
{"__isAuthError":true,"code":"email_not_confirmed","level":"error","message":"Supabase auth login error: Email not confirmed","name":"AuthApiError","service":"mobile-app-builder-backend","stack":"AuthApiError: Email not confirmed\n    at handleError (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:102:9)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async _handleRequest (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:195:5)\n    at async _request (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\lib\\fetch.ts:157:16)\n    at async SupabaseAuthClient.signInWithPassword (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\node_modules\\@supabase\\auth-js\\src\\GoTrueClient.ts:573:15)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:89:52)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","status":400,"timestamp":"2025-07-31T00:30:40.502Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:40.502Z"}
{"level":"error","message":"Login error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:40.502Z"}
{"level":"error","message":"Login endpoint error: Email not confirmed","service":"mobile-app-builder-backend","stack":"Error: Email not confirmed\n    at Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\supabaseAuth.ts:96:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async Function.login (C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\services\\auth.ts:176:16)\n    at async C:\\Users\\<USER>\\Downloads\\noryon ai app builder\\backend\\src\\routes\\auth.ts:84:20","timestamp":"2025-07-31T00:30:40.503Z"}
