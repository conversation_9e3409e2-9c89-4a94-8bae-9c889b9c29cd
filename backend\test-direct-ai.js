// Direct test of AI service without going through HTTP
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import the AI service directly
const { AIService } = require('./dist/services/aiService');

async function testDirectAI() {
  console.log('🔬 Testing AI Service Directly...');
  
  try {
    console.log('   🔑 API Keys loaded:');
    console.log('   - OpenAI:', process.env.OPENAI_API_KEY ? '✅ Present' : '❌ Missing');
    console.log('   - Anthropic:', process.env.ANTHROPIC_API_KEY ? '✅ Present' : '❌ Missing');
    console.log('   - OpenAI Model:', process.env.OPENAI_MODEL || 'gpt-4o-mini (default)');
    console.log('   - Anthropic Model:', process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022 (default)');
    
    console.log('\n   🤖 Initializing AI Service...');
    const aiService = new AIService();
    
    console.log('   💬 Testing simple chat...');
    const chatRequest = {
      message: "Hello! Can you help me create a simple React Native button?",
      sessionId: "test-direct-session",
      projectId: "test-project",
      includeContext: false
    };
    
    console.log('   📤 Sending chat request...');
    const startTime = Date.now();
    
    const response = await aiService.chat(chatRequest);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('   ✅ AI Response received!');
    console.log('   ⏱️  Response time:', duration + 'ms');
    console.log('   📄 Response preview:');
    console.log('   ' + response.substring(0, 300) + (response.length > 300 ? '...' : ''));
    
    return true;
    
  } catch (error) {
    console.log('   ❌ Direct AI test failed:', error.message);
    console.log('   🔍 Error details:', error);
    return false;
  }
}

// Run the test
testDirectAI()
  .then(success => {
    if (success) {
      console.log('\n🎉 Direct AI test successful! The AI service is working.');
    } else {
      console.log('\n💥 Direct AI test failed. Check the error details above.');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });
