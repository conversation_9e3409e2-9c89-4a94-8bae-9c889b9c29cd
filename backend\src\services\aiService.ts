import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { Pinecone } from '@pinecone-database/pinecone';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import {
  AIProvider,
  ProjectContext,
  AIInteraction,
  AISession,
  GeneratedCode,
  CodeDiff,
  CodeFix,
  RefactoredCode,
  Suggestion,
  AIContextVector,
  GenerateCodeRequest,
  ExplainCodeRequest,
  FixCodeRequest,
  ModifyCodeRequest,
  ChatRequest,
  DiffChange
} from '../types';

export class AIService {
  private openai?: OpenAI;
  private anthropic?: Anthropic;
  private pinecone?: Pinecone;
  private sessions: Map<string, AISession> = new Map();
  private contextVectors: Map<string, AIContextVector[]> = new Map();

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders(): void {
    try {
      // Initialize OpenAI
      if (process.env.OPENAI_API_KEY) {
        this.openai = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY,
        });
        logger.info('OpenAI client initialized');
      }

      // Initialize Anthropic
      if (process.env.ANTHROPIC_API_KEY) {
        this.anthropic = new Anthropic({
          apiKey: process.env.ANTHROPIC_API_KEY,
        });
        logger.info('Anthropic client initialized');
      }

      // Initialize Pinecone for vector database
      if (process.env.PINECONE_API_KEY) {
        this.pinecone = new Pinecone({
          apiKey: process.env.PINECONE_API_KEY,
        });
        logger.info('Pinecone client initialized');
      }
    } catch (error) {
      logger.error('Failed to initialize AI providers:', error);
    }
  }

  async generateCode(request: GenerateCodeRequest): Promise<GeneratedCode> {
    try {
      const sessionId = request.sessionId || uuidv4();
      const session = await this.getOrCreateSession(sessionId, request.projectId);
      
      // Get project context
      const context = await this.buildProjectContext(request.projectId);
      
      // Create system prompt for React Native code generation
      const systemPrompt = this.buildSystemPrompt(context, request.language);
      
      // Build user prompt with context
      const userPrompt = this.buildUserPrompt(request, context);
      
      // Generate code using preferred AI provider
      const generatedCode = await this.callAIProvider(systemPrompt, userPrompt, session);
      
      // Store interaction in session
      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'prompt',
        content: request.prompt,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'response',
        content: generatedCode.code,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      // Update context vectors
      await this.updateContextVectors(sessionId, request.prompt, generatedCode.code);

      return generatedCode;
    } catch (error) {
      logger.error('Error generating code:', error);
      throw new Error(`Failed to generate code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async explainCode(request: ExplainCodeRequest): Promise<string> {
    try {
      const sessionId = request.sessionId || uuidv4();
      const session = await this.getOrCreateSession(sessionId, request.projectId);
      
      const systemPrompt = `You are an expert React Native developer. Explain the provided code clearly and concisely. 
      Focus on what the code does, how it works, and any React Native specific concepts.
      Provide explanations that are helpful for developers of all skill levels.`;
      
      const userPrompt = `Please explain this ${request.language} code:\n\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\nFile: ${request.filePath || 'Unknown'}`;
      
      const explanation = await this.callAIProviderForText(systemPrompt, userPrompt, session);
      
      // Store interaction
      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'prompt',
        content: `Explain code: ${request.code.substring(0, 100)}...`,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'response',
        content: explanation,
        timestamp: new Date(),
      });

      return explanation;
    } catch (error) {
      logger.error('Error explaining code:', error);
      throw new Error(`Failed to explain code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async fixCode(request: FixCodeRequest): Promise<CodeFix> {
    try {
      const sessionId = request.sessionId || uuidv4();
      const session = await this.getOrCreateSession(sessionId, request.projectId);
      
      const systemPrompt = `You are an expert React Native developer. Fix the provided code based on the error message.
      Return only the corrected code with a brief explanation of what was fixed.
      Ensure the fix follows React Native best practices and maintains the original functionality.`;
      
      const userPrompt = `Fix this ${request.language} code that has an error:\n\nError: ${request.error}\n\nCode:\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\nFile: ${request.filePath}`;
      
      const response = await this.callAIProviderForText(systemPrompt, userPrompt, session);
      
      // Parse the response to extract fixed code and explanation
      const codeFix = this.parseCodeFixResponse(response, request);
      
      // Store interaction
      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'prompt',
        content: `Fix error: ${request.error}`,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'response',
        content: codeFix.fixedCode,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      return codeFix;
    } catch (error) {
      logger.error('Error fixing code:', error);
      throw new Error(`Failed to fix code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async modifyCode(request: ModifyCodeRequest): Promise<CodeDiff> {
    try {
      const sessionId = request.sessionId || uuidv4();
      const session = await this.getOrCreateSession(sessionId, request.projectId);
      
      const systemPrompt = `You are an expert React Native developer. Modify the provided code according to the instruction.
      Return the modified code and explain what changes were made.
      Ensure the modifications follow React Native best practices and maintain code quality.`;
      
      const userPrompt = `Modify this ${request.language} code:\n\nInstruction: ${request.instruction}\n\nCurrent code:\n\`\`\`${request.language}\n${request.code}\n\`\`\`\n\nFile: ${request.filePath}`;
      
      const response = await this.callAIProviderForText(systemPrompt, userPrompt, session);
      
      // Parse the response to create a code diff
      const codeDiff = this.createCodeDiff(request.code, response, request.filePath);
      
      // Store interaction
      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'prompt',
        content: `Modify code: ${request.instruction}`,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'response',
        content: codeDiff.newCode,
        timestamp: new Date(),
        metadata: {
          filePath: request.filePath,
          language: request.language,
        },
      });

      return codeDiff;
    } catch (error) {
      logger.error('Error modifying code:', error);
      throw new Error(`Failed to modify code: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async chat(request: ChatRequest): Promise<string> {
    try {
      const sessionId = request.sessionId || uuidv4();
      const session = await this.getOrCreateSession(sessionId, request.projectId);
      
      let systemPrompt = `You are an AI assistant specialized in React Native development. 
      You help developers build mobile applications using React Native.
      Provide helpful, accurate, and practical advice.`;
      
      let userPrompt = request.message;
      
      // Include project context if requested
      if (request.includeContext) {
        const context = await this.buildProjectContext(request.projectId);
        systemPrompt += `\n\nProject Context:\n${this.formatProjectContext(context)}`;
      }
      
      // Include conversation history
      const conversationHistory = this.formatConversationHistory(session);
      if (conversationHistory) {
        userPrompt = `${conversationHistory}\n\nUser: ${request.message}`;
      }
      
      const response = await this.callAIProviderForText(systemPrompt, userPrompt, session);
      
      // Store interaction
      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'prompt',
        content: request.message,
        timestamp: new Date(),
      });

      await this.storeInteraction(session, {
        id: uuidv4(),
        sessionId,
        type: 'response',
        content: response,
        timestamp: new Date(),
      });

      return response;
    } catch (error) {
      logger.error('Error in chat:', error);
      throw new Error(`Failed to process chat: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async suggestImprovements(code: string, language: string, filePath: string): Promise<Suggestion[]> {
    try {
      const systemPrompt = `You are an expert React Native code reviewer. 
      Analyze the provided code and suggest improvements for performance, security, best practices, and potential bugs.
      Return suggestions in a structured format.`;
      
      const userPrompt = `Review this ${language} code and suggest improvements:\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nFile: ${filePath}`;
      
      const response = await this.callAIProviderForText(systemPrompt, userPrompt);
      
      // Parse response into structured suggestions
      return this.parseSuggestions(response, filePath);
    } catch (error) {
      logger.error('Error suggesting improvements:', error);
      return [];
    }
  }

  private async getOrCreateSession(sessionId: string, projectId: string): Promise<AISession> {
    if (this.sessions.has(sessionId)) {
      return this.sessions.get(sessionId)!;
    }
    
    const session: AISession = {
      id: sessionId,
      projectId,
      userId: '', // This should be set from the request context
      conversationHistory: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.sessions.set(sessionId, session);
    return session;
  }

  private async buildProjectContext(projectId: string): Promise<ProjectContext> {
    // This would typically fetch from the database
    // For now, return a mock context
    return {
      projectId,
      files: [],
      dependencies: ['react', 'react-native'],
      reactNativeVersion: '0.72.0',
      targetPlatforms: ['ios', 'android'],
      recentChanges: [],
    };
  }

  private buildSystemPrompt(context: ProjectContext, language?: string): string {
    return `You are an expert React Native developer. Generate high-quality, production-ready code.

Project Context:
- React Native Version: ${context.reactNativeVersion}
- Target Platforms: ${context.targetPlatforms.join(', ')}
- Dependencies: ${context.dependencies.join(', ')}
- Language: ${language || 'TypeScript'}

Guidelines:
1. Use TypeScript when possible for better type safety
2. Follow React Native best practices and conventions
3. Use functional components with hooks
4. Implement proper error handling
5. Add appropriate comments for complex logic
6. Ensure cross-platform compatibility
7. Use proper styling with StyleSheet
8. Handle platform-specific code when necessary

Return only the code with brief explanations when needed.`;
  }

  private buildUserPrompt(request: GenerateCodeRequest, context: ProjectContext): string {
    let prompt = `Generate React Native code for: ${request.prompt}`;
    
    if (request.filePath) {
      prompt += `\nFile: ${request.filePath}`;
    }
    
    if (request.context?.existingCode) {
      prompt += `\n\nExisting code to modify or extend:\n\`\`\`\n${request.context.existingCode}\n\`\`\``;
    }
    
    if (request.context?.targetPlatform) {
      prompt += `\nTarget platform: ${request.context.targetPlatform}`;
    }
    
    return prompt;
  }

  private async callAIProvider(systemPrompt: string, userPrompt: string, session?: AISession): Promise<GeneratedCode> {
    // Try OpenAI first, then Anthropic
    if (this.openai) {
      return await this.callOpenAI(systemPrompt, userPrompt);
    } else if (this.anthropic) {
      return await this.callAnthropic(systemPrompt, userPrompt);
    } else {
      throw new Error('No AI provider available');
    }
  }

  private async callAIProviderForText(systemPrompt: string, userPrompt: string, session?: AISession): Promise<string> {
    // Try OpenAI first, then Anthropic
    if (this.openai) {
      return await this.callOpenAIForText(systemPrompt, userPrompt);
    } else if (this.anthropic) {
      return await this.callAnthropicForText(systemPrompt, userPrompt);
    } else {
      throw new Error('No AI provider available');
    }
  }

  private async callOpenAI(systemPrompt: string, userPrompt: string): Promise<GeneratedCode> {
    const model = process.env.OPENAI_MODEL || 'gpt-4o-mini';
    const response = await this.openai!.chat.completions.create({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const content = response.choices[0]?.message?.content || '';
    return this.parseGeneratedCode(content);
  }

  private async callOpenAIForText(systemPrompt: string, userPrompt: string): Promise<string> {
    const model = process.env.OPENAI_MODEL || 'gpt-4o-mini';
    const response = await this.openai!.chat.completions.create({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 1500,
    });

    return response.choices[0]?.message?.content || '';
  }

  private async callAnthropic(systemPrompt: string, userPrompt: string): Promise<GeneratedCode> {
    const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022';
    const response = await this.anthropic!.messages.create({
      model,
      max_tokens: 2000,
      system: systemPrompt,
      messages: [
        { role: 'user', content: userPrompt }
      ],
    });

    const content = response.content[0]?.type === 'text' ? response.content[0].text : '';
    return this.parseGeneratedCode(content);
  }

  private async callAnthropicForText(systemPrompt: string, userPrompt: string): Promise<string> {
    const model = process.env.ANTHROPIC_MODEL || 'claude-3-5-sonnet-20241022';
    const response = await this.anthropic!.messages.create({
      model,
      max_tokens: 1500,
      system: systemPrompt,
      messages: [
        { role: 'user', content: userPrompt }
      ],
    });

    return response.content[0]?.type === 'text' ? response.content[0].text : '';
  }

  private parseGeneratedCode(content: string): GeneratedCode {
    // Extract code blocks from the response
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const matches = Array.from(content.matchAll(codeBlockRegex));
    
    if (matches.length > 0) {
      const [, language, code] = matches[0];
      return {
        code: code.trim(),
        language: language || 'typescript',
        explanation: content.replace(codeBlockRegex, '').trim(),
        confidence: 0.8,
      };
    }
    
    // If no code blocks found, treat entire content as code
    return {
      code: content.trim(),
      language: 'typescript',
      explanation: 'Generated code',
      confidence: 0.6,
    };
  }

  private parseCodeFixResponse(response: string, request: FixCodeRequest): CodeFix {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const matches = Array.from(response.matchAll(codeBlockRegex));
    
    let fixedCode = request.code;
    let explanation = response;
    
    if (matches.length > 0) {
      fixedCode = matches[0][2].trim();
      explanation = response.replace(codeBlockRegex, '').trim();
    }
    
    return {
      originalCode: request.code,
      fixedCode,
      explanation,
      confidence: 0.8,
      filePath: request.filePath,
    };
  }

  private createCodeDiff(oldCode: string, newCodeResponse: string, filePath: string): CodeDiff {
    // Extract new code from response
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const matches = Array.from(newCodeResponse.matchAll(codeBlockRegex));
    
    let newCode = newCodeResponse;
    let summary = 'Code modified';
    
    if (matches.length > 0) {
      newCode = matches[0][2].trim();
      summary = newCodeResponse.replace(codeBlockRegex, '').trim();
    }
    
    // Simple diff calculation (in a real implementation, use a proper diff library)
    const changes: DiffChange[] = [];
    const oldLines = oldCode.split('\n');
    const newLines = newCode.split('\n');
    
    // Basic line-by-line comparison
    const maxLines = Math.max(oldLines.length, newLines.length);
    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';
      
      if (oldLine !== newLine) {
        if (!oldLine) {
          changes.push({
            type: 'add',
            lineNumber: i + 1,
            content: newLine,
          });
        } else if (!newLine) {
          changes.push({
            type: 'remove',
            lineNumber: i + 1,
            content: oldLine,
          });
        } else {
          changes.push({
            type: 'modify',
            lineNumber: i + 1,
            content: newLine,
            context: oldLine,
          });
        }
      }
    }
    
    return {
      filePath,
      oldCode,
      newCode,
      changes,
      summary,
    };
  }

  private parseSuggestions(response: string, filePath: string): Suggestion[] {
    // Simple parsing - in a real implementation, this would be more sophisticated
    const suggestions: Suggestion[] = [];
    const lines = response.split('\n');
    
    for (const line of lines) {
      if (line.includes('performance') || line.includes('Performance')) {
        suggestions.push({
          type: 'performance',
          title: 'Performance Improvement',
          description: line.trim(),
          filePath,
          severity: 'medium',
        });
      } else if (line.includes('security') || line.includes('Security')) {
        suggestions.push({
          type: 'security',
          title: 'Security Issue',
          description: line.trim(),
          filePath,
          severity: 'high',
        });
      } else if (line.includes('bug') || line.includes('Bug') || line.includes('error')) {
        suggestions.push({
          type: 'bug-fix',
          title: 'Potential Bug',
          description: line.trim(),
          filePath,
          severity: 'high',
        });
      } else if (line.includes('best practice') || line.includes('Best practice')) {
        suggestions.push({
          type: 'best-practice',
          title: 'Best Practice',
          description: line.trim(),
          filePath,
          severity: 'low',
        });
      }
    }
    
    return suggestions;
  }

  private formatProjectContext(context: ProjectContext): string {
    return `Project: ${context.projectId}
React Native: ${context.reactNativeVersion}
Platforms: ${context.targetPlatforms.join(', ')}
Dependencies: ${context.dependencies.join(', ')}
Files: ${context.files.length} files`;
  }

  private formatConversationHistory(session: AISession): string {
    const recentHistory = session.conversationHistory.slice(-6); // Last 6 interactions
    return recentHistory
      .map(interaction => `${interaction.type === 'prompt' ? 'User' : 'Assistant'}: ${interaction.content}`)
      .join('\n');
  }

  private async storeInteraction(session: AISession, interaction: AIInteraction): Promise<void> {
    session.conversationHistory.push(interaction);
    session.updatedAt = new Date();
    
    // Keep only last 50 interactions to prevent memory issues
    if (session.conversationHistory.length > 50) {
      session.conversationHistory = session.conversationHistory.slice(-50);
    }
  }

  private async updateContextVectors(sessionId: string, prompt: string, response: string): Promise<void> {
    // In a real implementation, this would generate embeddings and store them in Pinecone
    // For now, we'll just store the text content
    if (!this.contextVectors.has(sessionId)) {
      this.contextVectors.set(sessionId, []);
    }
    
    const vectors = this.contextVectors.get(sessionId)!;
    
    vectors.push({
      id: uuidv4(),
      projectId: '',
      sessionId,
      content: prompt,
      embedding: [], // Would be generated using OpenAI embeddings API
      metadata: {
        type: 'prompt',
        timestamp: new Date(),
      },
    });
    
    vectors.push({
      id: uuidv4(),
      projectId: '',
      sessionId,
      content: response,
      embedding: [], // Would be generated using OpenAI embeddings API
      metadata: {
        type: 'response',
        timestamp: new Date(),
      },
    });
  }

  // Context management methods
  async maintainContext(sessionId: string, interaction: AIInteraction): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      await this.storeInteraction(session, interaction);
    }
  }

  async getSessionHistory(sessionId: string): Promise<AIInteraction[]> {
    const session = this.sessions.get(sessionId);
    return session?.conversationHistory || [];
  }

  async clearSession(sessionId: string): Promise<void> {
    this.sessions.delete(sessionId);
    this.contextVectors.delete(sessionId);
  }

  // Health check method
  async healthCheck(): Promise<{ openai: boolean; anthropic: boolean; pinecone: boolean }> {
    return {
      openai: !!this.openai,
      anthropic: !!this.anthropic,
      pinecone: !!this.pinecone,
    };
  }
}

export const aiService = new AIService();