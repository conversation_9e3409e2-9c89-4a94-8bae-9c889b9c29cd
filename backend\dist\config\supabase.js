"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabaseHelpers = exports.initializeDatabase = exports.pool = exports.db = exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const logger_1 = require("@/utils/logger");
class SupabaseAdapter {
    constructor(client) {
        this.client = client;
    }
    async query(text, params = []) {
        try {
            logger_1.logger.warn('Direct SQL query attempted on Supabase adapter:', { query: text.substring(0, 100) });
            throw new Error('Direct SQL queries not supported with Supabase. Use Supabase client methods instead.');
        }
        catch (error) {
            logger_1.logger.error('Supabase query error:', error);
            throw error;
        }
    }
    async close() {
        logger_1.logger.info('Supabase client connection closed');
    }
}
exports.pool = {
    query: async (text, params) => {
        return await exports.db.query(text, params);
    },
    connect: async () => {
        return {
            query: async (text, params) => {
                return await exports.db.query(text, params);
            },
            release: () => { }
        };
    }
};
const initializeDatabase = async () => {
    try {
        logger_1.logger.info('Initializing Supabase database connection...');
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
        if (!supabaseUrl || !supabaseAnonKey) {
            throw new Error('Missing Supabase configuration. Please check SUPABASE_URL and SUPABASE_ANON_KEY environment variables.');
        }
        exports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseAnonKey, {
            auth: {
                autoRefreshToken: true,
                persistSession: false,
            },
        });
        const { data, error } = await exports.supabase.from('user_profiles').select('count').limit(1);
        if (error && !error.message.includes('relation "user_profiles" does not exist')) {
            throw error;
        }
        exports.db = new SupabaseAdapter(exports.supabase);
        logger_1.logger.info('Supabase database connection established successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to connect to Supabase:', error);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
exports.supabaseHelpers = {
    async createUser(userData) {
        const { data, error } = await exports.supabase.from('users').insert(userData).select().single();
        if (error)
            throw error;
        return data;
    },
    async getUserById(id) {
        const { data, error } = await exports.supabase.from('users').select('*').eq('id', id).single();
        if (error && error.code !== 'PGRST116')
            throw error;
        return data;
    },
    async getUserByEmail(email) {
        const { data, error } = await exports.supabase.from('users').select('*').eq('email', email).single();
        if (error && error.code !== 'PGRST116')
            throw error;
        return data;
    },
    async createProject(projectData) {
        const { data, error } = await exports.supabase.from('projects').insert(projectData).select().single();
        if (error)
            throw error;
        return data;
    },
    async getProjectById(id) {
        const { data, error } = await exports.supabase.from('projects').select('*').eq('id', id).single();
        if (error && error.code !== 'PGRST116')
            throw error;
        return data;
    },
    async getUserProjects(userId) {
        const { data, error } = await exports.supabase.from('projects').select('*').eq('user_id', userId);
        if (error)
            throw error;
        return data || [];
    },
    async createProjectFile(fileData) {
        const { data, error } = await exports.supabase.from('project_files').insert(fileData).select().single();
        if (error)
            throw error;
        return data;
    },
    async getProjectFiles(projectId) {
        const { data, error } = await exports.supabase.from('project_files').select('*').eq('project_id', projectId);
        if (error)
            throw error;
        return data || [];
    },
    async updateProjectFile(id, updates) {
        const { data, error } = await exports.supabase.from('project_files').update(updates).eq('id', id).select().single();
        if (error)
            throw error;
        return data;
    },
    async deleteProjectFile(id) {
        const { error } = await exports.supabase.from('project_files').delete().eq('id', id);
        if (error)
            throw error;
    }
};
//# sourceMappingURL=supabase.js.map